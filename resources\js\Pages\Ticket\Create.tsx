import React, { useState } from 'react';
import { Plus } from 'lucide-react';
import { Button } from '@/Components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/Components/ui/dialog';
import { Input } from '@/Components/ui/input';
import { Textarea } from '@/Components/ui/textarea';
import { Label } from '@/Components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/Components/ui/select';

import { router } from '@inertiajs/react';


// Define Public type (visibility)
type PublicType = 'public' | 'private';

const publicOptions: { value: PublicType; label: string }[] = [
  { value: 'public', label: 'Public - Visible to everyone' },
  { value: 'private', label: 'Private - Only visible to staff' },
];

interface CreateTicketDialogProps {
  categories?: any[];
  tags?: any[];
}

export function CreateTicketDialog({ categories = [], tags = [] }: CreateTicketDialogProps) {

  const [open, setOpen] = useState(false);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<number | ''>('');
  const [isPublic, setIsPublic] = useState<PublicType>('public');
  const [selectedTag, setSelectedTag] = useState<number | ''>('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (!selectedCategory) {
      newErrors.category = 'Please select a category';
    }

    if (!selectedTag) {
      newErrors.tag = 'Please select a tag';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (!validateForm()) return;

    router.post('/posts', {
      title: title.trim(),
      content: description.trim(),
      categories: [selectedCategory],
      tags: [selectedTag],
      is_published: isPublic === 'public' ? 1 : 0,
    }, {
      onSuccess: () => {
        // Reset form
        setTitle('');
        setDescription('');
        setSelectedCategory('');
        setSelectedTag('');
        setIsPublic('public');
        setErrors({});
        setOpen(false);
      },
      onError: (formErrors) => {
        setErrors(formErrors as Record<string, string>);
      }
    });
  };



  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          Create Ticket
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Create New Support Ticket</DialogTitle>
          <DialogDescription>
            Describe your issue or question and our support team will help you.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-6 py-4">
          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title">Title *</Label>
            <Input
              id="title"
              placeholder="Brief description of your issue"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className={errors.title ? 'border-red-500' : ''}
            />
            {errors.title && (
              <p className="text-sm text-red-500">{errors.title}</p>
            )}
          </div>

          {/* Category and Priority */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="category">Category *</Label>
              <Select
                value={selectedCategory.toString()}
                onValueChange={(value) => setSelectedCategory(parseInt(value))}
              >
                <SelectTrigger className={errors.category ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((cat) => (
                    <SelectItem key={cat.id} value={cat.id.toString()}>
                      {cat.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.category && (
                <p className="text-sm text-red-500">{errors.category}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="visibility">Visibility *</Label>
              <Select value={isPublic} onValueChange={(value) => setIsPublic(value as PublicType)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {publicOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description *</Label>
            <Textarea
              id="description"
              placeholder="Please provide detailed information about your issue, including steps to reproduce if applicable..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className={errors.description ? 'border-red-500' : ''}
              rows={4}
            />
            {errors.description && (
              <p className="text-sm text-red-500">{errors.description}</p>
            )}
          </div>

          {/* Tags */}
          <div className="space-y-2">
            <Label htmlFor="tags">Tag *</Label>
            <Select
              value={selectedTag.toString()}
              onValueChange={(value) => setSelectedTag(parseInt(value))}
            >
              <SelectTrigger className={errors.tag ? 'border-red-500' : ''}>
                <SelectValue placeholder="Select a tag" />
              </SelectTrigger>
              <SelectContent>
                {tags.map((tag) => (
                  <SelectItem key={tag.id} value={tag.id.toString()}>
                    {tag.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.tag && (
              <p className="text-sm text-red-500">{errors.tag}</p>
            )}
          </div>
        </div>

        <div className="flex justify-end gap-3">
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={!title.trim() || !description.trim()}
          >
            Create Ticket
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}