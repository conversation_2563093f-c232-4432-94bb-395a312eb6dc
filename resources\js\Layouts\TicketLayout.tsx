import React from 'react';
import { usePage } from '@inertiajs/react';
import AppLayout from '@/Layouts/AppLayout';
import { FilterSidebar } from '@/Pages/Ticket/FilterSidebar';

interface TicketLayoutProps {
  title: string;
  categories?: any[];
  departments?: any[];
  users?: any[];
  filters?: {
    status?: string;
    priority?: string;
    department?: string;
    assignee?: string;
    category?: string;
    search?: string;
    myTickets?: boolean;
    sortBy?: string;
  };
  notifications?: any[];
  children: React.ReactNode;
  showSidebar?: boolean;
}

const TicketLayout: React.FC<TicketLayoutProps> = ({
  title,
  categories = [],
  departments = [],
  users = [],
  filters = {},
  notifications = [],
  children,
  showSidebar = true,
}) => {
  const { props } = usePage();

  return (
    <AppLayout
      title={title}
      canLogin={true}
      canRegister={true}
      notifications={notifications}
    >
      <div className="min-h-screen bg-background">
        <div className="flex">
          {/* Sidebar */}
          {showSidebar && (
            <FilterSidebar
              categories={categories}
              departments={departments}
              users={users}
              filters={filters}
              currentUser={(props as any)?.auth?.user}
            />
          )}

          {/* Main Content */}
          <main className={`flex-1 ${showSidebar ? 'ml-0' : ''}`}>
            {children}
          </main>
        </div>
      </div>
    </AppLayout>
  );
};

export default TicketLayout;
