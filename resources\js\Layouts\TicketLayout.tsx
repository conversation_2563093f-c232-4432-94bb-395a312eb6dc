import React from 'react';
import { usePage, router } from '@inertiajs/react';
import AppLayout from '@/Layouts/AppLayout';
import { FilterSidebar } from '@/Pages/Ticket/FilterSidebar';
import { Button } from '@/Components/ui/button';
import { ArrowLeft } from 'lucide-react';

interface TicketLayoutProps {
  title: string;
  categories?: any[];
  departments?: any[];
  users?: any[];
  filters?: {
    status?: string;
    priority?: string;
    department?: string;
    assignee?: string;
    category?: string;
    search?: string;
    myTickets?: boolean;
    sortBy?: string;
  };
  notifications?: any[];
  children: React.ReactNode;
  showSidebar?: boolean;
  backUrl?: string; // URL để back về trang trước
  backLabel?: string; // Label cho back button
}

const TicketLayout: React.FC<TicketLayoutProps> = ({
  title,
  categories = [],
  departments = [],
  users = [],
  filters = {},
  notifications = [],
  children,
  showSidebar = true,
  backUrl,
  backLabel = 'Back',
}) => {
  const { props } = usePage();

  return (
    <AppLayout
      title={title}
      canLogin={true}
      canRegister={true}
      notifications={notifications}
    >
      <div className="min-h-screen bg-background">
        <div className="flex">
          {/* Sidebar */}
          {showSidebar && (
            <FilterSidebar
              categories={categories}
              departments={departments}
              users={users}
              filters={filters}
              currentUser={(props as any)?.auth?.user}
            />
          )}

          {/* Main Content */}
          <main className={`flex-1 ${showSidebar ? 'ml-0' : ''}`}>
            {/* Back Button */}
            {/* {backUrl && (
              <div className="p-4 border-b">
                <Button
                  variant="ghost"
                  onClick={() => router.get(backUrl)}
                  className="gap-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                  {backLabel}
                </Button>
              </div>
            )} */}
            {children}
          </main>
        </div>
      </div>
    </AppLayout>
  );
};

export default TicketLayout;
